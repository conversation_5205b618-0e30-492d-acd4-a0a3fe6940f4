import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AIService {
  static final AIService _instance = AIService._internal();
  late SharedPreferences _prefs;
  bool _isInitialized = false;
  GenerativeModel? _generativeModel;

  AIService._internal();

  factory AIService() => _instance;

  static Future<AIService> create() async {
    final service = AIService();
    await service.initialize();
    return service;
  }

  Future<void> initialize() async {
    if (!_isInitialized) {
      _prefs = await SharedPreferences.getInstance();

      // Initialize the Vertex AI model
      _generativeModel = FirebaseVertexAI.instance.generativeModel(
        model: 'gemini-2.0-flash',
        generationConfig: GenerationConfig(
          temperature: 0.7,
        ),
      );

      _isInitialized = true;
    }
  }

  Future<String> getChatResponse(String userMessage,
      {String? sessionId}) async {
    // Make sure initialization is complete
    if (!_isInitialized || _generativeModel == null) {
      await initialize();
    }

    try {
      // Build conversation history with context
      final conversationHistory =
          _buildConversationHistory(userMessage, sessionId);

      // Generate content using Vertex AI with conversation context
      final response =
          await _generativeModel!.generateContent(conversationHistory);
      final answer = response.text ?? 'Sorry, I couldn\'t generate a response.';

      // Only save non-greeting messages
      if (userMessage.isNotEmpty && !answer.startsWith('Hello, I am MimiAI')) {
        await _saveChat(userMessage, answer, sessionId: sessionId);
      }

      return answer;
    } catch (e) {
      if (kDebugMode) {
        print('Error generating response: $e');
      }
      return 'Error generating response: $e';
    }
  }

  /// Builds conversation history including system instruction and previous messages
  List<Content> _buildConversationHistory(
      String userMessage, String? sessionId) {
    final conversationHistory = <Content>[];

    // Add system instruction first
    conversationHistory.add(Content.text(
        'You are a Mimi Bland. You are a self-mastery and emotional intelligence coach. You are not a therapist. Whenever you can\'t solve people\'s problems and you need to suggest a therapist, ask them to book a call with Mimi from https://mimibland.london/contact/ . Do not use the word therapist.'));

    // Get previous messages from this session (if any)
    if (sessionId != null) {
      final sessionMessages = _getSessionMessages(sessionId);

      // Add previous conversation messages (limit to last 10 exchanges to avoid token limits)
      final limitedMessages =
          sessionMessages.take(20).toList(); // 20 messages = ~10 exchanges

      for (final message in limitedMessages.reversed) {
        // Add user message
        if (message['question'] != null &&
            message['question'].toString().isNotEmpty) {
          conversationHistory.add(Content.text(message['question']));
        }

        // Add AI response
        if (message['answer'] != null &&
            message['answer'].toString().isNotEmpty) {
          conversationHistory.add(Content.text(message['answer']));
        }
      }
    }

    // Add current user message
    conversationHistory.add(Content.text(userMessage));

    return conversationHistory;
  }

  /// Gets messages for a specific session, sorted by timestamp (oldest first)
  List<Map<String, dynamic>> _getSessionMessages(String sessionId) {
    if (!_isInitialized) return [];

    final List<String> chats = _prefs.getStringList('chats') ?? [];
    final sessionChats = chats
        .map((c) => Map<String, dynamic>.from(jsonDecode(c)))
        .where((chat) => chat['sessionId'] == sessionId)
        .toList();

    // Sort by timestamp (oldest first for conversation flow)
    sessionChats.sort((a, b) => DateTime.parse(a['timestamp'])
        .compareTo(DateTime.parse(b['timestamp'])));

    return sessionChats;
  }

  Future<void> _saveChat(String question, String answer,
      {String? sessionId}) async {
    if (!_isInitialized) await initialize();

    // Don't save if it's just a greeting
    if (question.isEmpty && answer.startsWith('Hello, I am MimiAI')) {
      return;
    }

    final List<String> chats = _prefs.getStringList('chats') ?? [];

    final newChat = jsonEncode({
      'timestamp': DateTime.now().toIso8601String(),
      'sessionId': sessionId ?? DateTime.now().toIso8601String(),
      'type': 'chat',
      'question': question,
      'answer': answer,
    });

    chats.add(newChat);
    await _prefs.setStringList('chats', chats);
  }

  List<Map<String, dynamic>> getAllChats() {
    if (!_isInitialized) return [];

    final List<String> chats = _prefs.getStringList('chats') ?? [];
    final parsed = chats
        .map((c) => Map<String, dynamic>.from(jsonDecode(c)))
        .where((chat) =>
            // Filter out any greeting messages that might have been saved previously
            !(chat['question'] == '' &&
                (chat['answer'] as String).startsWith('Hello, I am MimiAI')))
        .toList();

    // Sort by timestamp, most recent first
    parsed.sort((a, b) => DateTime.parse(b['timestamp'])
        .compareTo(DateTime.parse(a['timestamp'])));

    return parsed;
  }

  Future<void> clearChats() async {
    if (!_isInitialized) await initialize();
    await _prefs.remove('chats');
  }

  Future<void> deleteChat(String sessionId) async {
    if (!_isInitialized) await initialize();
    final List<String> chats = _prefs.getStringList('chats') ?? [];

    // Filter out all messages from this session
    final updatedChats = chats.where((chatString) {
      final chat = jsonDecode(chatString);
      return chat['sessionId'] != sessionId;
    }).toList();

    await _prefs.setStringList('chats', updatedChats);
  }
}
