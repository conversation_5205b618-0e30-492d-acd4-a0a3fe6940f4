name: mimi_app
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.6

environment:
  sdk: ^3.5.3

dependencies:
  flutter:
    sdk: flutter

  # Core Dependencies
  cupertino_icons: ^1.0.2
  intl: ^0.19.0
  path: ^1.9.0
  collection: ^1.18.0
  flutter_local_notifications: ^16.3.2
  timezone: ^0.9.2

  # Backend & Firebase
  firebase_core: ^3.8.1
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.4
  firebase_storage: ^12.3.4
  google_sign_in: ^6.2.2
  firebase_vertexai: ^1.6.0

  # State Management
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  freezed_annotation: ^2.4.4

  # Navigation & Routing
  go_router: ^14.3.0

  # Database & Storage
  drift: ^2.23.1
  drift_flutter: ^0.2.4
  sqlite3_flutter_libs: ^0.5.28
  shared_preferences: ^2.3.3
  path_provider: ^2.1.5

  # Analytics & Monitoring
  mixpanel_flutter: ^2.3.3
  sentry_flutter: ^8.9.0

  # UI Components & Widgets
  flutter_svg: ^2.0.14
  font_awesome_flutter: '>= 4.7.0'
  line_icons: ^2.0.3
  smooth_page_indicator: ^1.2.0+3
  material_dialogs: ^1.1.5
  loading_indicator: ^3.1.1
  animations: ^2.0.11
  rive: ^0.13.20
  fl_chart: ^0.70.1
  cached_network_image: ^3.4.1
  flutter_markdown: ^0.7.5
  table_calendar: ^3.1.3

  # Media & Audio
  flutter_sound: ^9.17.8
  audio_session: ^0.1.23
  audio_service: ^0.18.16
  just_audio: ^0.9.43
  just_audio_background: ^0.0.1-beta.14
  audioplayers: ^6.1.0
  record: ^5.2.0
  video_player: ^2.9.2
  youtube_player_flutter: ^9.1.1

  # Localization
  easy_localization: ^3.0.7

  # Utilities & Helpers
  crypto: ^3.0.6
  file_picker: ^8.1.5
  permission_handler: ^11.3.1
  fluttertoast: ^8.0.3
  toastification: ^2.3.0
  share_plus: ^10.1.3
  url_launcher: ^6.3.1
  web_socket_channel: ^3.0.1
  webview_flutter: ^4.13.0
  in_app_review: ^2.0.10
  json_annotation: ^4.9.0
  http: ^1.2.2
  image_picker: ^1.1.2

  # App Setup & Configuration
  rename: ^3.0.2
  flutter_native_splash: ^2.4.2
  flutter_flavorizr: ^2.2.3

  # Payments & Subscriptions
  purchases_flutter: ^8.3.1
  purchases_ui_flutter: ^8.3.1

  # New dependency
  flutter_expandable_fab: ^2.0.0
  firebase_app_check: ^0.3.2+5

dev_dependencies:
  sentry_dart_plugin: ^3.1.0
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  riverpod_generator: ^2.6.3
  build_runner: ^2.4.13
  freezed: ^2.5.7
  json_serializable: ^6.9.0
  drift_dev: ^2.23.1

  # Linting
  custom_lint: ^0.7.0
  riverpod_lint: ^2.6.1

flutter:
  uses-material-design: true
  disable-swift-package-manager: true

  assets:
   - assets/icons/
   - assets/images/
   - assets/images/breathwork/
   - assets/images/cover/
   - assets/images/onboarding/
   - assets/images/home/
   - assets/images/headers/
   - assets/images/home_header/
   - assets/images/mood/
   - assets/audio/affirmations/
   - assets/audio/meditation/
   - assets/audio/breathwork/


  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
    - family: Quicksand
      fonts:
        - asset: assets/fonts/Quicksand-Light.ttf
          weight: 300
        - asset: assets/fonts/Quicksand-Regular.ttf
          weight: 400
        - asset: assets/fonts/Quicksand-Medium.ttf
          weight: 500
        - asset: assets/fonts/Quicksand-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Quicksand-Bold.ttf
          weight: 700
sentry:
  upload_debug_symbols: true
  upload_source_maps: true
  project: flutter
  org: mimi-bland
